<?php

/**
 * Simple test script to verify the wallet API
 * Run with: php test_wallet_api.php
 */

require_once 'vendor/autoload.php';

use Illuminate\Http\Request;
use App\Http\Controllers\Api\WalletController;
use App\Services\WalletService;
use App\Services\CustomerService;

// Mock test - this would normally be done through HTTP requests
echo "=== Customer Wallet API Test ===\n\n";

// Test customer ID
$customerId = 1;

echo "Testing wallet API endpoints:\n";
echo "1. GET /api/v2/wallet/{$customerId} - Basic wallet info\n";
echo "2. GET /api/v2/wallet/{$customerId}/balance - Balance only\n";
echo "3. GET /api/v2/wallet/{$customerId}/transactions - Transaction history\n";
echo "4. GET /api/v2/wallet/{$customerId}/details - Enhanced wallet with history (NEW)\n\n";

echo "Expected Response Format for Enhanced API:\n";
echo json_encode([
    'success' => true,
    'data' => [
        'wallet_details' => [
            'available_balance' => 4.00,
            'usable_balance' => 4.00,
            'locked_balance' => 0.00,
            'currency' => 'INR'
        ],
        'wallet_history' => [
            'data' => [
                [
                    'date' => '14-07-2025',
                    'description' => 'Cash ₹ 1 deposited by Admin Manager',
                    'amount' => '₹1.00',
                    'credit_debit' => 'Cr',
                    'payment_mode' => 'Cash',
                    'transacted_by' => 'Admin Manager'
                ]
            ],
            'pagination' => [
                'current_page' => 1,
                'per_page' => 15,
                'total' => 4,
                'last_page' => 1
            ]
        ]
    ]
], JSON_PRETTY_PRINT);

echo "\n\n=== API Usage Examples ===\n\n";

echo "1. Get wallet details with history:\n";
echo "curl -X GET 'http://localhost:8000/api/v2/wallet/1/details' \\\n";
echo "  -H 'Authorization: Bearer YOUR_TOKEN' \\\n";
echo "  -H 'Accept: application/json'\n\n";

echo "2. Get wallet details with filters:\n";
echo "curl -X GET 'http://localhost:8000/api/v2/wallet/1/details?type=credit&per_page=10' \\\n";
echo "  -H 'Authorization: Bearer YOUR_TOKEN' \\\n";
echo "  -H 'Accept: application/json'\n\n";

echo "3. Get wallet details with date range:\n";
echo "curl -X GET 'http://localhost:8000/api/v2/wallet/1/details?date_from=2025-01-01&date_to=2025-12-31' \\\n";
echo "  -H 'Authorization: Bearer YOUR_TOKEN' \\\n";
echo "  -H 'Accept: application/json'\n\n";

echo "=== Database Schema Required ===\n\n";
echo "The API uses the 'customer_wallet' table with these key fields:\n";
echo "- fk_customer_code: Customer ID\n";
echo "- wallet_amount: Transaction amount\n";
echo "- amount_type: 'cr' (credit), 'dr' (debit), 'lock' (locked)\n";
echo "- description: Transaction description\n";
echo "- payment_type: Payment method (cash, online, etc.)\n";
echo "- payment_date: Transaction date\n";
echo "- reference_no: Transaction reference\n\n";

echo "=== Features ===\n";
echo "✅ Available Balance - Total credits minus debits\n";
echo "✅ Usable Balance - Available balance minus locked amount\n";
echo "✅ Locked Balance - Amount locked for pending orders\n";
echo "✅ Transaction History - Paginated with filters\n";
echo "✅ Date Range Filtering\n";
echo "✅ Transaction Type Filtering (credit/debit/lock)\n";
echo "✅ UI-Ready Format - Matches your design requirements\n\n";

echo "Test completed!\n";
