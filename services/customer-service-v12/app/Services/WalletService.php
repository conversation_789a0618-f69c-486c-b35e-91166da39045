<?php

namespace App\Services;

use App\Events\Wallet\WalletDeposited;
use App\Events\Wallet\WalletWithdrawn;
use App\Exceptions\Wallet\InsufficientBalanceException;
use App\Exceptions\Wallet\WalletNotFoundException;
use App\Models\Customer;
use App\Models\CustomerWallet;
use App\Models\WalletTransaction;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Psr\Log\LoggerInterface;
use Exception;

/**
 * Wallet Service
 *
 * This service encapsulates all wallet-related business logic.
 */
class WalletService
{
    /**
     * Create a new WalletService instance.
     */
    public function __construct(
        protected Dispatcher $events,
        protected LoggerInterface $logger,
        protected CustomerService $customerService
    ) {
    }

    /**
     * Get a customer's wallet balance and details
     *
     * @param int $customerId
     * @return array
     * @throws WalletNotFoundException
     */
    public function getWallet(int $customerId): array
    {
        try {
            // Get customer to verify existence
            $customer = $this->customerService->getCustomerById($customerId);

            // Calculate wallet balance from transactions
            $walletData = DB::table('customer_wallet')
                ->select([
                    DB::raw("SUM(CASE WHEN amount_type = 'cr' THEN wallet_amount ELSE 0 END) as total_credit"),
                    DB::raw("SUM(CASE WHEN amount_type = 'dr' THEN wallet_amount ELSE 0 END) as total_debit"),
                    DB::raw("SUM(CASE WHEN amount_type = 'lock' THEN wallet_amount ELSE 0 END) as total_locked")
                ])
                ->where('fk_customer_code', $customerId)
                ->first();

            $totalCredit = (float) ($walletData->total_credit ?? 0);
            $totalDebit = (float) ($walletData->total_debit ?? 0);
            $totalLocked = (float) ($walletData->total_locked ?? 0);

            $availableBalance = $totalCredit - $totalDebit;
            $usableBalance = $availableBalance - $totalLocked; // Available balance minus locked amount

            return [
                'customer_id' => $customerId,
                'customer_code' => $customerId,
                'balance' => $availableBalance,
                'available_balance' => $availableBalance,
                'usable_balance' => max(0, $usableBalance), // Ensure it's never negative
                'locked_balance' => $totalLocked,
                'total_credit' => $totalCredit,
                'total_debit' => $totalDebit,
                'currency' => 'INR',
                'status' => 'active',
                'wallet_details' => [
                    'available_balance' => $availableBalance,
                    'usable_balance' => max(0, $usableBalance),
                    'locked_balance' => $totalLocked
                ]
            ];
        } catch (Exception $e) {
            $this->logger->error('Error getting wallet', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'customer_id' => $customerId
            ]);

            throw $e;
        }
    }

    /**
     * Get wallet balance for a customer
     *
     * @param int $customerId
     * @return float
     */
    public function getBalance(int $customerId): float
    {
        $walletData = $this->getWallet($customerId);
        return $walletData['balance'];
    }

    /**
     * Deposit to a customer's wallet
     *
     * @param int $customerId
     * @param float $amount
     * @param string $description
     * @param string $transactionId
     * @return array
     * @throws WalletNotFoundException
     */
    public function deposit(int $customerId, float $amount, string $description = '', string $transactionId = ''): array
    {
        try {
            // Validate amount
            if ($amount <= 0) {
                throw new Exception("Deposit amount must be greater than zero");
            }

            // Get customer to verify existence
            $customer = $this->customerService->getCustomerById($customerId);

            // Start transaction
            DB::beginTransaction();

            // Get current balance
            $currentWallet = $this->getWallet($customerId);
            $oldBalance = $currentWallet['balance'];

            // Insert credit transaction
            DB::table('customer_wallet')->insert([
                'company_id' => 1, // Default company ID
                'unit_id' => 1, // Default unit ID
                'fk_customer_code' => $customerId,
                'wallet_amount' => $amount,
                'amount_type' => 'cr',
                'reference_no' => $transactionId ?: uniqid('dep_'),
                'payment_date' => now()->toDateString(),
                'description' => $description ?: 'Wallet deposit',
                'created_by' => 1, // System user
                'created_date' => now(),
                'updated_by' => 1,
                'updated_date' => now(),
                'context' => 'customer',
                'payment_type' => 'online'
            ]);

            // Commit transaction
            DB::commit();

            // Get updated wallet
            $updatedWallet = $this->getWallet($customerId);

            // Log
            $this->logger->info('Wallet deposit', [
                'customer_id' => $customerId,
                'amount' => $amount,
                'old_balance' => $oldBalance,
                'new_balance' => $updatedWallet['balance'],
                'transaction_id' => $transactionId
            ]);

            return $updatedWallet;
        } catch (Exception $e) {
            // Rollback transaction
            DB::rollBack();

            // Log error
            $this->logger->error('Error depositing to wallet', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'customer_id' => $customerId,
                'amount' => $amount
            ]);

            throw $e;
        }
    }

    /**
     * Withdraw from a customer's wallet
     *
     * @param int $customerId
     * @param float $amount
     * @param string $description
     * @param string $transactionId
     * @return array
     * @throws WalletNotFoundException
     * @throws InsufficientBalanceException
     */
    public function withdraw(int $customerId, float $amount, string $description = '', string $transactionId = ''): array
    {
        try {
            // Validate amount
            if ($amount <= 0) {
                throw new Exception("Withdrawal amount must be greater than zero");
            }

            // Get customer to verify existence
            $customer = $this->customerService->getCustomerById($customerId);

            // Start transaction
            DB::beginTransaction();

            // Get current balance
            $currentWallet = $this->getWallet($customerId);
            $oldBalance = $currentWallet['balance'];

            // Check balance
            if ($oldBalance < $amount) {
                throw new InsufficientBalanceException("Insufficient balance for withdrawal. Available: ₹{$oldBalance}, Requested: ₹{$amount}");
            }

            // Insert debit transaction
            DB::table('customer_wallet')->insert([
                'company_id' => 1, // Default company ID
                'unit_id' => 1, // Default unit ID
                'fk_customer_code' => $customerId,
                'wallet_amount' => $amount,
                'amount_type' => 'dr',
                'reference_no' => $transactionId ?: uniqid('wit_'),
                'payment_date' => now()->toDateString(),
                'description' => $description ?: 'Wallet withdrawal',
                'created_by' => 1, // System user
                'created_date' => now(),
                'updated_by' => 1,
                'updated_date' => now(),
                'context' => 'customer',
                'payment_type' => 'wallet'
            ]);

            // Commit transaction
            DB::commit();

            // Get updated wallet
            $updatedWallet = $this->getWallet($customerId);

            // Log
            $this->logger->info('Wallet withdrawal', [
                'customer_id' => $customerId,
                'amount' => $amount,
                'old_balance' => $oldBalance,
                'new_balance' => $updatedWallet['balance'],
                'transaction_id' => $transactionId
            ]);

            return $updatedWallet;
        } catch (Exception $e) {
            // Rollback transaction
            DB::rollBack();

            // Log error
            $this->logger->error('Error withdrawing from wallet', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'customer_id' => $customerId,
                'amount' => $amount
            ]);

            throw $e;
        }
    }

    /**
     * Get wallet transaction history
     *
     * @param int $customerId
     * @param array $filters
     * @param int $perPage
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getTransactionHistory(int $customerId, array $filters = [], int $perPage = 15)
    {
        $query = DB::table('customer_wallet')
            ->where('fk_customer_code', $customerId)
            ->select([
                'customer_wallet_id as id',
                'fk_customer_code as customer_code',
                'wallet_amount as amount',
                'amount_type as type',
                'description',
                'reference_no as transaction_id',
                'payment_date as date',
                'payment_type',
                'context',
                'created_date as created_at',
                'updated_date as updated_at'
            ]);

        // Apply filters
        if (isset($filters['type'])) {
            // Map API type to database type
            $typeMap = [
                'deposit' => 'cr',
                'credit' => 'cr',
                'withdrawal' => 'dr',
                'debit' => 'dr',
                'lock' => 'lock'
            ];
            $dbType = $typeMap[$filters['type']] ?? $filters['type'];
            $query->where('amount_type', $dbType);
        }

        if (isset($filters['date_from'])) {
            $query->whereDate('created_date', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_date', '<=', $filters['date_to']);
        }

        // Order by
        $query->orderBy('created_date', 'desc');

        // Get paginated results
        $total = $query->count();
        $transactions = $query->offset(($perPage * (request('page', 1) - 1)))->limit($perPage)->get();

        // Transform the data
        $transformedTransactions = $transactions->map(function ($transaction) {
            return [
                'id' => $transaction->id,
                'customer_code' => $transaction->customer_code,
                'amount' => (float) $transaction->amount,
                'type' => $this->mapDbTypeToApiType($transaction->type),
                'description' => $transaction->description,
                'transaction_id' => $transaction->transaction_id,
                'date' => $transaction->date,
                'payment_type' => $transaction->payment_type,
                'context' => $transaction->context,
                'created_at' => $transaction->created_at,
                'updated_at' => $transaction->updated_at
            ];
        });

        return new \Illuminate\Pagination\LengthAwarePaginator(
            $transformedTransactions,
            $total,
            $perPage,
            request('page', 1),
            ['path' => request()->url()]
        );
    }

    /**
     * Get wallet details with transaction history in UI format
     *
     * @param int $customerId
     * @param array $filters
     * @param int $perPage
     * @return array
     */
    public function getWalletWithHistory(int $customerId, array $filters = [], int $perPage = 15): array
    {
        // Get wallet details
        $walletDetails = $this->getWallet($customerId);

        // Get transaction history
        $transactionHistory = $this->getTransactionHistory($customerId, $filters, $perPage);

        // Transform transactions for UI
        $transformedTransactions = collect($transactionHistory->items())->map(function ($transaction) {
            $creditDebit = match($transaction['type']) {
                'credit' => 'Cr',
                'debit' => 'Dr',
                'lock' => 'Lock',
                default => 'Unknown'
            };

            return [
                'date' => date('d-m-Y', strtotime($transaction['date'])),
                'description' => $transaction['description'],
                'amount' => '₹' . number_format($transaction['amount'], 2),
                'credit_debit' => $creditDebit,
                'payment_mode' => ucfirst($transaction['payment_type'] ?? 'Cash'),
                'transacted_by' => $this->extractTransactedBy($transaction['description'])
            ];
        });

        return [
            'wallet_details' => [
                'available_balance' => $walletDetails['available_balance'],
                'usable_balance' => $walletDetails['usable_balance'],
                'locked_balance' => $walletDetails['locked_balance'],
                'currency' => $walletDetails['currency']
            ],
            'wallet_history' => [
                'data' => $transformedTransactions,
                'pagination' => [
                    'current_page' => $transactionHistory->currentPage(),
                    'per_page' => $transactionHistory->perPage(),
                    'total' => $transactionHistory->total(),
                    'last_page' => $transactionHistory->lastPage()
                ]
            ]
        ];
    }

    /**
     * Extract transacted by from description
     *
     * @param string $description
     * @return string
     */
    private function extractTransactedBy(string $description): string
    {
        // Look for patterns like "by Admin Manager", "by System", etc.
        if (preg_match('/by\s+([^|]+)/i', $description, $matches)) {
            return trim($matches[1]);
        }

        // Default fallback
        return 'System';
    }

    /**
     * Map database amount_type to API type
     *
     * @param string|null $dbType
     * @return string
     */
    private function mapDbTypeToApiType(?string $dbType): string
    {
        if ($dbType === null) {
            return 'unknown';
        }

        return match ($dbType) {
            'cr' => 'credit',
            'dr' => 'debit',
            'lock' => 'lock',
            default => $dbType
        };
    }
}
