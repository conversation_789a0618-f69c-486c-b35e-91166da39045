<?php

namespace App\Utils;

use Exception;
use InvalidArgumentException;

/**
 * JWT Decoder Utility
 * 
 * Custom JWT decoder for Keycloak tokens without external packages.
 * Handles JWT validation, decoding, and claim extraction.
 */
class JwtDecoder
{
    /**
     * Decode JWT token without signature verification
     * 
     * @param string $jwt
     * @return array
     * @throws InvalidArgumentException
     */
    public static function decode(string $jwt): array
    {
        $parts = explode('.', $jwt);
        
        if (count($parts) !== 3) {
            throw new InvalidArgumentException('Invalid JWT format');
        }

        [$header, $payload, $signature] = $parts;

        // Decode header
        $decodedHeader = self::base64UrlDecode($header);
        $headerData = json_decode($decodedHeader, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new InvalidArgumentException('Invalid JWT header');
        }

        // Decode payload
        $decodedPayload = self::base64UrlDecode($payload);
        $payloadData = json_decode($decodedPayload, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new InvalidArgumentException('Invalid JWT payload');
        }

        return [
            'header' => $headerData,
            'payload' => $payloadData,
            'signature' => $signature,
            'raw' => [
                'header' => $header,
                'payload' => $payload,
                'signature' => $signature
            ]
        ];
    }

    /**
     * Validate JWT token structure and expiration
     * 
     * @param string $jwt
     * @return array
     */
    public static function validate(string $jwt): array
    {
        try {
            $decoded = self::decode($jwt);
            $payload = $decoded['payload'];
            
            $validation = [
                'valid' => true,
                'expired' => false,
                'errors' => [],
                'payload' => $payload,
                'header' => $decoded['header']
            ];

            // Check expiration
            if (isset($payload['exp'])) {
                $currentTime = time();
                if ($currentTime >= $payload['exp']) {
                    $validation['expired'] = true;
                    $validation['valid'] = false;
                    $validation['errors'][] = 'Token has expired';
                }
            }

            // Check issued at time
            if (isset($payload['iat'])) {
                $currentTime = time();
                if ($payload['iat'] > $currentTime + 300) { // 5 minutes tolerance
                    $validation['valid'] = false;
                    $validation['errors'][] = 'Token issued in the future';
                }
            }

            // Check not before
            if (isset($payload['nbf'])) {
                $currentTime = time();
                if ($currentTime < $payload['nbf']) {
                    $validation['valid'] = false;
                    $validation['errors'][] = 'Token not yet valid';
                }
            }

            return $validation;

        } catch (Exception $e) {
            return [
                'valid' => false,
                'expired' => false,
                'errors' => ['Invalid token format: ' . $e->getMessage()],
                'payload' => null,
                'header' => null
            ];
        }
    }

    /**
     * Extract user information from Keycloak JWT
     * 
     * @param array $payload
     * @return array
     */
    public static function extractUserInfo(array $payload): array
    {
        return [
            'user_id' => $payload['sub'] ?? null,
            'username' => $payload['preferred_username'] ?? $payload['username'] ?? null,
            'email' => $payload['email'] ?? null,
            'email_verified' => $payload['email_verified'] ?? false,
            'first_name' => $payload['given_name'] ?? null,
            'last_name' => $payload['family_name'] ?? null,
            'full_name' => $payload['name'] ?? null,
            'client_id' => $payload['azp'] ?? $payload['aud'] ?? null,
            'issuer' => $payload['iss'] ?? null,
            'realm' => self::extractRealm($payload['iss'] ?? ''),
            'session_id' => $payload['session_state'] ?? null,
            'issued_at' => $payload['iat'] ?? null,
            'expires_at' => $payload['exp'] ?? null,
            'old_sso_user_id' => self::extractOldSsoUserId($payload),
        ];
    }

    /**
     * Extract roles from Keycloak JWT
     * 
     * @param array $payload
     * @return array
     */
    public static function extractRoles(array $payload): array
    {
        $roles = [
            'realm_roles' => [],
            'resource_roles' => [],
            'all_roles' => []
        ];

        // Extract realm roles
        if (isset($payload['realm_access']['roles'])) {
            $roles['realm_roles'] = $payload['realm_access']['roles'];
            $roles['all_roles'] = array_merge($roles['all_roles'], $payload['realm_access']['roles']);
        }

        // Extract resource/client roles
        if (isset($payload['resource_access'])) {
            foreach ($payload['resource_access'] as $resource => $access) {
                if (isset($access['roles'])) {
                    $roles['resource_roles'][$resource] = $access['roles'];
                    $roles['all_roles'] = array_merge($roles['all_roles'], $access['roles']);
                }
            }
        }

        $roles['all_roles'] = array_unique($roles['all_roles']);

        return $roles;
    }

    /**
     * Check if user has specific role
     * 
     * @param array $payload
     * @param string $role
     * @param string|null $resource
     * @return bool
     */
    public static function hasRole(array $payload, string $role, ?string $resource = null): bool
    {
        $roles = self::extractRoles($payload);

        if ($resource) {
            return in_array($role, $roles['resource_roles'][$resource] ?? []);
        }

        return in_array($role, $roles['all_roles']);
    }

    /**
     * Check if user has admin privileges
     * 
     * @param array $payload
     * @return bool
     */
    public static function isAdmin(array $payload): bool
    {
        $adminRoles = [
            'realm-admin',
            'admin',
            'super-admin',
            'manage-users',
            'manage-realm'
        ];

        $roles = self::extractRoles($payload);
        
        foreach ($adminRoles as $adminRole) {
            if (in_array($adminRole, $roles['all_roles'])) {
                return true;
            }
        }

        return false;
    }

    /**
     * Extract Old SSO User ID from custom claims
     * 
     * @param array $payload
     * @return string|null
     */
    private static function extractOldSsoUserId(array $payload): ?string
    {
        // Check various possible locations for Old SSO User ID
        $possibleKeys = [
            'oldSsoUserId',
            'old_sso_user_id',
            'legacy_user_id',
            'custom_user_id',
            'external_user_id'
        ];

        foreach ($possibleKeys as $key) {
            if (isset($payload[$key])) {
                return (string) $payload[$key];
            }
        }

        // Check in custom attributes
        if (isset($payload['custom_attributes'])) {
            foreach ($possibleKeys as $key) {
                if (isset($payload['custom_attributes'][$key])) {
                    return (string) $payload['custom_attributes'][$key];
                }
            }
        }

        return null;
    }

    /**
     * Extract realm from issuer URL
     * 
     * @param string $issuer
     * @return string|null
     */
    private static function extractRealm(string $issuer): ?string
    {
        if (preg_match('/\/realms\/([^\/]+)/', $issuer, $matches)) {
            return $matches[1];
        }

        return null;
    }

    /**
     * Base64 URL decode
     * 
     * @param string $data
     * @return string
     */
    private static function base64UrlDecode(string $data): string
    {
        $remainder = strlen($data) % 4;
        if ($remainder) {
            $data .= str_repeat('=', 4 - $remainder);
        }

        return base64_decode(strtr($data, '-_', '+/'));
    }

    /**
     * Get token expiration time in human readable format
     * 
     * @param array $payload
     * @return array
     */
    public static function getExpirationInfo(array $payload): array
    {
        if (!isset($payload['exp'])) {
            return [
                'expires_at' => null,
                'expires_in_seconds' => null,
                'expires_in_minutes' => null,
                'is_expired' => false,
                'formatted_expiry' => null
            ];
        }

        $expiresAt = $payload['exp'];
        $currentTime = time();
        $expiresInSeconds = $expiresAt - $currentTime;

        return [
            'expires_at' => $expiresAt,
            'expires_in_seconds' => max(0, $expiresInSeconds),
            'expires_in_minutes' => max(0, round($expiresInSeconds / 60, 2)),
            'is_expired' => $expiresInSeconds <= 0,
            'formatted_expiry' => date('Y-m-d H:i:s', $expiresAt)
        ];
    }
}
