<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Support\Facades\DB;

class OrderManagementPriorityTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /**
     * Test that specific order_no returns immediately
     */
    public function test_specific_order_no_returns_immediately()
    {
        // Create test customer
        $customerId = DB::table('customers')->insertGetId([
            'customer_name' => 'Test Customer',
            'email_address' => '<EMAIL>',
            'phone' => '1234567890',
            'status' => 1,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Create test product
        $productId = DB::table('products')->insertGetId([
            'name' => 'Test Product',
            'unit_price' => 100.00,
            'status' => 1,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Create test order
        $orderNo = 'TEST' . time();
        $orderId = DB::table('orders')->insertGetId([
            'order_no' => $orderNo,
            'customer_code' => $customerId,
            'product_code' => $productId,
            'product_name' => 'Test Product',
            'order_date' => now()->toDateString(),
            'order_status' => 'new',
            'delivery_status' => 'pending',
            'amount' => 100.00,
            'quantity' => 1,
            'ship_address' => 'Test Student, Test Address',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Test with specific order_no
        $response = $this->getJson("/api/v2/order-management/customer/{$customerId}?order_status=new?order_no={$orderNo}");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'filters' => [
                            'specific_order_no' => $orderNo,
                            'priority_mode' => true
                        ]
                    ]
                ]);

        // Verify the specific order is returned
        $responseData = $response->json();
        $this->assertNotEmpty($responseData['data']['orders']);
        
        // Check that at least one category contains the order
        $foundOrder = false;
        foreach (['upcoming', 'cancelled', 'other'] as $category) {
            if (!empty($responseData['data']['orders'][$category])) {
                foreach ($responseData['data']['orders'][$category] as $order) {
                    if ($order['order_no'] === $orderNo) {
                        $foundOrder = true;
                        break 2;
                    }
                }
            }
        }
        
        $this->assertTrue($foundOrder, 'Specific order should be found in response');
    }

    /**
     * Test include_cancelled parameter
     */
    public function test_include_cancelled_parameter()
    {
        // Create test customer
        $customerId = DB::table('customers')->insertGetId([
            'customer_name' => 'Test Customer',
            'email_address' => '<EMAIL>',
            'phone' => '1234567890',
            'status' => 1,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Create test product
        $productId = DB::table('products')->insertGetId([
            'name' => 'Test Product',
            'unit_price' => 100.00,
            'status' => 1,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Create cancelled order
        DB::table('orders')->insert([
            'order_no' => 'CANCELLED' . time(),
            'customer_code' => $customerId,
            'product_code' => $productId,
            'product_name' => 'Test Product',
            'order_date' => now()->toDateString(),
            'order_status' => 'cancelled',
            'delivery_status' => 'cancelled',
            'amount' => 100.00,
            'quantity' => 1,
            'ship_address' => 'Test Student, Test Address',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Test with include_cancelled=false
        $response = $this->getJson("/api/v2/order-management/customer/{$customerId}?include_cancelled=false");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'filters' => [
                            'include_cancelled' => false
                        ]
                    ]
                ]);

        // Verify cancelled orders are excluded
        $responseData = $response->json();
        $this->assertEquals(0, $responseData['data']['summary']['cancelled_orders']);
    }

    /**
     * Test order_status filter
     */
    public function test_order_status_filter()
    {
        // Create test customer
        $customerId = DB::table('customers')->insertGetId([
            'customer_name' => 'Test Customer',
            'email_address' => '<EMAIL>',
            'phone' => '1234567890',
            'status' => 1,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Create test product
        $productId = DB::table('products')->insertGetId([
            'name' => 'Test Product',
            'unit_price' => 100.00,
            'status' => 1,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Create orders with different statuses
        DB::table('orders')->insert([
            [
                'order_no' => 'NEW' . time(),
                'customer_code' => $customerId,
                'product_code' => $productId,
                'product_name' => 'Test Product',
                'order_date' => now()->toDateString(),
                'order_status' => 'new',
                'delivery_status' => 'pending',
                'amount' => 100.00,
                'quantity' => 1,
                'ship_address' => 'Test Student, Test Address',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'order_no' => 'CONFIRMED' . time(),
                'customer_code' => $customerId,
                'product_code' => $productId,
                'product_name' => 'Test Product',
                'order_date' => now()->toDateString(),
                'order_status' => 'confirmed',
                'delivery_status' => 'pending',
                'amount' => 100.00,
                'quantity' => 1,
                'ship_address' => 'Test Student, Test Address',
                'created_at' => now(),
                'updated_at' => now()
            ]
        ]);

        // Test with order_status filter
        $response = $this->getJson("/api/v2/order-management/customer/{$customerId}?order_status=new");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'filters' => [
                            'order_status' => 'new'
                        ]
                    ]
                ]);

        // The response should contain filtered results
        $this->assertNotEmpty($response->json('data.orders'));
    }

    /**
     * Test that non-existent specific order returns 404
     */
    public function test_non_existent_specific_order_returns_404()
    {
        // Create test customer
        $customerId = DB::table('customers')->insertGetId([
            'customer_name' => 'Test Customer',
            'email_address' => '<EMAIL>',
            'phone' => '1234567890',
            'status' => 1,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Test with non-existent order_no
        $response = $this->getJson("/api/v2/order-management/customer/{$customerId}?order_status=new?order_no=NONEXISTENT123");

        $response->assertStatus(404)
                ->assertJson([
                    'success' => false,
                    'message' => 'Specific order not found'
                ]);
    }
}
